#include <Arduino.h>
#include <TFT_eSPI.h>

// Create TFT instance
TFT_eSPI tft = TFT_eSPI();

// Backlight pin
#define TFT_BL 21

// Function declarations
void testDisplayConfigurations();
void drawColorBars(int rotation);
void testTextRendering(int rotation);
void testColorInversion();
void testRGBOrder();
void finalCalibrationTest();

void setup() {
  delay(1000);  // Wait for power stabilization

  // STEP 1: Turn on backlight
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  delay(100);

  // STEP 2: Initialize TFT
  tft.init();
  delay(100);

  // STEP 3: Test different configurations
  testDisplayConfigurations();
}

void testDisplayConfigurations() {
  // Test 1: Basic color test with different rotations
  for (int rotation = 0; rotation < 4; rotation++) {
    tft.setRotation(rotation);

    // Clear screen
    tft.fillScreen(TFT_BLACK);
    delay(500);

    // Test primary colors
    tft.fillScreen(TFT_RED);
    delay(1000);

    tft.fillScreen(TFT_GREEN);
    delay(1000);

    tft.fillScreen(TFT_BLUE);
    delay(1000);

    tft.fillScreen(TFT_WHITE);
    delay(1000);

    // Test color bars
    drawColorBars(rotation);
    delay(2000);

    // Test text rendering
    testTextRendering(rotation);
    delay(3000);
  }

  // Test 2: Color inversion test
  testColorInversion();

  // Test 3: RGB order test
  testRGBOrder();

  // Test 4: Final calibration
  finalCalibrationTest();
}

void drawColorBars(int rotation) {
  tft.fillScreen(TFT_BLACK);

  int width = tft.width();
  int height = tft.height();
  int barHeight = height / 8;

  // Draw color bars
  tft.fillRect(0, 0 * barHeight, width, barHeight, TFT_RED);
  tft.fillRect(0, 1 * barHeight, width, barHeight, TFT_GREEN);
  tft.fillRect(0, 2 * barHeight, width, barHeight, TFT_BLUE);
  tft.fillRect(0, 3 * barHeight, width, barHeight, TFT_YELLOW);
  tft.fillRect(0, 4 * barHeight, width, barHeight, TFT_CYAN);
  tft.fillRect(0, 5 * barHeight, width, barHeight, TFT_MAGENTA);
  tft.fillRect(0, 6 * barHeight, width, barHeight, TFT_WHITE);
  tft.fillRect(0, 7 * barHeight, width, barHeight, TFT_BLACK);

  // Add rotation label
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(10, height - 30);
  tft.printf("Rotation: %d", rotation);
}

void testTextRendering(int rotation) {
  tft.fillScreen(TFT_BLACK);

  // Test different text sizes and colors
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(1);
  tft.setCursor(10, 10);
  tft.printf("Rotation: %d", rotation);

  tft.setTextSize(2);
  tft.setCursor(10, 30);
  tft.println("ESP32-2432S028R");

  tft.setTextColor(TFT_CYAN, TFT_BLACK);
  tft.setTextSize(1);
  tft.setCursor(10, 60);
  tft.println("TFT Display Test");

  tft.setTextColor(TFT_YELLOW, TFT_BLACK);
  tft.setCursor(10, 80);
  tft.printf("Width: %d", tft.width());

  tft.setCursor(10, 100);
  tft.printf("Height: %d", tft.height());

  // Test special characters
  tft.setTextColor(TFT_GREEN, TFT_BLACK);
  tft.setCursor(10, 120);
  tft.println("!@#$%^&*()");

  tft.setTextColor(TFT_MAGENTA, TFT_BLACK);
  tft.setCursor(10, 140);
  tft.println("1234567890");

  // Test Thai characters (if supported)
  tft.setTextColor(TFT_RED, TFT_BLACK);
  tft.setCursor(10, 160);
  tft.println("ABCDEFGHIJK");
}

void testColorInversion() {
  // Test with inversion OFF
  tft.invertDisplay(false);
  tft.fillScreen(TFT_BLACK);

  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(10, 50);
  tft.println("INVERSION OFF");

  tft.setTextSize(1);
  tft.setCursor(10, 80);
  tft.println("White text on black");

  delay(3000);

  // Test with inversion ON
  tft.invertDisplay(true);
  tft.fillScreen(TFT_BLACK);

  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(10, 50);
  tft.println("INVERSION ON");

  tft.setTextSize(1);
  tft.setCursor(10, 80);
  tft.println("Should look different");

  delay(3000);

  // Reset to normal
  tft.invertDisplay(false);
}

void testRGBOrder() {
  tft.fillScreen(TFT_BLACK);

  // Draw RGB test pattern
  int width = tft.width();
  int height = tft.height();

  // Red section
  tft.fillRect(0, 0, width/3, height, TFT_RED);

  // Green section
  tft.fillRect(width/3, 0, width/3, height, TFT_GREEN);

  // Blue section
  tft.fillRect(2*width/3, 0, width/3, height, TFT_BLUE);

  // Add labels
  tft.setTextColor(TFT_WHITE, TFT_RED);
  tft.setTextSize(2);
  tft.setCursor(10, height/2);
  tft.println("R");

  tft.setTextColor(TFT_WHITE, TFT_GREEN);
  tft.setCursor(width/3 + 10, height/2);
  tft.println("G");

  tft.setTextColor(TFT_WHITE, TFT_BLUE);
  tft.setCursor(2*width/3 + 10, height/2);
  tft.println("B");

  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(1);
  tft.setCursor(10, 10);
  tft.println("RGB Order Test");

  delay(5000);
}

void finalCalibrationTest() {
  // Set optimal rotation (landscape)
  tft.setRotation(1);
  tft.fillScreen(TFT_BLACK);

  // Draw calibration pattern
  int width = tft.width();
  int height = tft.height();

  // Border
  tft.drawRect(0, 0, width, height, TFT_WHITE);
  tft.drawRect(1, 1, width-2, height-2, TFT_WHITE);

  // Corner markers
  tft.fillCircle(10, 10, 5, TFT_RED);
  tft.fillCircle(width-10, 10, 5, TFT_GREEN);
  tft.fillCircle(10, height-10, 5, TFT_BLUE);
  tft.fillCircle(width-10, height-10, 5, TFT_YELLOW);

  // Center cross
  tft.drawLine(width/2-20, height/2, width/2+20, height/2, TFT_CYAN);
  tft.drawLine(width/2, height/2-20, width/2, height/2+20, TFT_CYAN);

  // Title
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(width/2-80, 30);
  tft.println("CALIBRATION");

  // Status
  tft.setTextSize(1);
  tft.setCursor(width/2-60, 60);
  tft.println("Display Calibrated");

  // Resolution info
  tft.setCursor(width/2-40, 80);
  tft.printf("%dx%d", width, height);

  // Instructions
  tft.setTextColor(TFT_YELLOW, TFT_BLACK);
  tft.setCursor(10, height-40);
  tft.println("Check corners and center alignment");

  tft.setTextColor(TFT_GREEN, TFT_BLACK);
  tft.setCursor(10, height-20);
  tft.println("If OK, display is properly configured");
}

void loop() {
  // Continuous status indicator
  static unsigned long lastBlink = 0;
  static bool blinkState = false;

  if (millis() - lastBlink > 2000) {
    blinkState = !blinkState;

    // Just blink the corner LEDs
    if (blinkState) {
      tft.fillCircle(10, 10, 5, TFT_WHITE);
      tft.fillCircle(tft.width()-10, 10, 5, TFT_WHITE);
      tft.fillCircle(10, tft.height()-10, 5, TFT_WHITE);
      tft.fillCircle(tft.width()-10, tft.height()-10, 5, TFT_WHITE);
    } else {
      tft.fillCircle(10, 10, 5, TFT_RED);
      tft.fillCircle(tft.width()-10, 10, 5, TFT_GREEN);
      tft.fillCircle(10, tft.height()-10, 5, TFT_BLUE);
      tft.fillCircle(tft.width()-10, tft.height()-10, 5, TFT_YELLOW);
    }

    lastBlink = millis();
  }

  delay(10);
}
