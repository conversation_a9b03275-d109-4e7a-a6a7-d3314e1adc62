#include <Arduino.h>
#include <TFT_eSPI.h>

// Create TFT instance
TFT_eSPI tft = TFT_eSPI();

// Pin definitions for ESP32-2432S028R
#define TFT_BL 21  // Backlight pin

void setup() {
  // Serial.begin(115200);  // Commented out to avoid Serial issues
  delay(1000);

  // Serial.println("ESP32-2432S028R Display Test Starting...");

  // Initialize backlight pin
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);  // Turn on backlight
  // Serial.println("Backlight turned ON");

  // Initialize TFT
  // Serial.println("Initializing TFT...");
  tft.init();

  // Try different rotations
  for (int rotation = 0; rotation < 4; rotation++) {
    // Serial.printf("Testing rotation %d\n", rotation);

    tft.setRotation(rotation);
    tft.fillScreen(TFT_BLACK);
    delay(500);

    // Test basic colors
    tft.fillScreen(TFT_RED);
    delay(1000);

    tft.fillScreen(TFT_GREEN);
    delay(1000);

    tft.fillScreen(TFT_BLUE);
    delay(1000);

    tft.fillScreen(TFT_WHITE);
    delay(1000);

    // Test text
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.setTextSize(2);
    tft.setCursor(10, 10);
    tft.printf("Rotation: %d", rotation);
    tft.setCursor(10, 40);
    tft.println("ESP32-2432S028R");
    tft.setCursor(10, 70);
    tft.println("Display Test");

    delay(3000);
  }

  // Serial.println("Display test completed");
}

void loop() {
  // Blink test
  static unsigned long lastBlink = 0;
  static bool blinkState = false;

  if (millis() - lastBlink > 1000) {
    blinkState = !blinkState;

    if (blinkState) {
      tft.fillScreen(TFT_CYAN);
      tft.setTextColor(TFT_BLACK, TFT_CYAN);
      tft.setTextSize(3);
      tft.setCursor(20, 50);
      tft.println("WORKING!");
      tft.setTextSize(2);
      tft.setCursor(30, 100);
      tft.println("Display OK");
      // Serial.println("Display: CYAN");
    } else {
      tft.fillScreen(TFT_MAGENTA);
      tft.setTextColor(TFT_WHITE, TFT_MAGENTA);
      tft.setTextSize(3);
      tft.setCursor(20, 50);
      tft.println("ESP32");
      tft.setTextSize(2);
      tft.setCursor(30, 100);
      tft.println("2432S028R");
      // Serial.println("Display: MAGENTA");
    }

    lastBlink = millis();
  }

  delay(10);
}
