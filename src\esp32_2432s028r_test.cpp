#include <Arduino.h>
#include <TFT_eSPI.h>

// Create TFT instance
TFT_eSPI tft = TFT_eSPI();

// Backlight pin
#define TFT_BL 21

// Function declarations
void testESP32Display();
void testScreenBoundaries(int rotation, int width, int height);
void testTextPositioning(int rotation, int width, int height);
void findBestRotation();
void drawFullScreenPattern(int width, int height);

void setup() {
  delay(1000);

  // Turn on backlight
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  delay(100);

  // Initialize TFT
  tft.init();

  // Test different configurations for ESP32-2432S028R
  testESP32Display();
}

void testESP32Display() {
  // Test 1: Full screen test with different rotations
  for (int rotation = 0; rotation < 4; rotation++) {
    tft.setRotation(rotation);

    // Get actual screen dimensions
    int width = tft.width();
    int height = tft.height();

    // Clear screen
    tft.fillScreen(TFT_BLACK);
    delay(500);

    // Fill entire screen with color
    tft.fillScreen(TFT_RED);
    delay(1000);

    // Test screen boundaries
    testScreenBoundaries(rotation, width, height);
    delay(2000);

    // Test text positioning
    testTextPositioning(rotation, width, height);
    delay(2000);
  }

  // Find best rotation and test full screen
  findBestRotation();
}

void testScreenBoundaries(int rotation, int width, int height) {
  tft.fillScreen(TFT_BLACK);

  // Draw border around entire screen
  tft.drawRect(0, 0, width, height, TFT_WHITE);
  tft.drawRect(1, 1, width-2, height-2, TFT_WHITE);

  // Fill corners to test exact boundaries
  tft.fillRect(0, 0, 20, 20, TFT_RED);           // Top-left
  tft.fillRect(width-20, 0, 20, 20, TFT_GREEN);  // Top-right
  tft.fillRect(0, height-20, 20, 20, TFT_BLUE);  // Bottom-left
  tft.fillRect(width-20, height-20, 20, 20, TFT_YELLOW); // Bottom-right

  // Center cross
  tft.drawLine(width/2-10, height/2, width/2+10, height/2, TFT_CYAN);
  tft.drawLine(width/2, height/2-10, width/2, height/2+10, TFT_CYAN);

  // Display info
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(1);
  tft.setCursor(5, 25);
  tft.printf("Rot:%d %dx%d", rotation, width, height);

  // Test if we can draw at exact edges
  tft.drawPixel(0, 0, TFT_MAGENTA);
  tft.drawPixel(width-1, 0, TFT_MAGENTA);
  tft.drawPixel(0, height-1, TFT_MAGENTA);
  tft.drawPixel(width-1, height-1, TFT_MAGENTA);
}

void testTextPositioning(int rotation, int width, int height) {
  tft.fillScreen(TFT_BLACK);

  // Test text at different positions
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);

  // Top-left
  tft.setCursor(0, 0);
  tft.println("TL");

  // Top-right
  tft.setCursor(width-24, 0);
  tft.println("TR");

  // Bottom-left
  tft.setCursor(0, height-16);
  tft.println("BL");

  // Bottom-right
  tft.setCursor(width-24, height-16);
  tft.println("BR");

  // Center
  tft.setCursor(width/2-12, height/2-8);
  tft.println("C");

  // Display rotation info
  tft.setTextSize(1);
  tft.setCursor(width/2-30, height/2+10);
  tft.printf("Rotation: %d", rotation);

  tft.setCursor(width/2-30, height/2+25);
  tft.printf("Size: %dx%d", width, height);
}

void findBestRotation() {
  // Test rotation 1 (landscape) which should be 320x240
  tft.setRotation(1);

  int width = tft.width();
  int height = tft.height();

  // Clear screen
  tft.fillScreen(TFT_BLACK);

  // Draw full screen test pattern
  drawFullScreenPattern(width, height);

  // Add final test info
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("ESP32-2432S028R");

  tft.setTextSize(1);
  tft.setCursor(10, 35);
  tft.printf("Resolution: %dx%d", width, height);

  tft.setCursor(10, 50);
  tft.println("Full Screen Test");

  if (width == 320 && height == 240) {
    tft.setTextColor(TFT_GREEN, TFT_BLACK);
    tft.setCursor(10, 65);
    tft.println("CORRECT RESOLUTION!");
  } else {
    tft.setTextColor(TFT_RED, TFT_BLACK);
    tft.setCursor(10, 65);
    tft.println("WRONG RESOLUTION!");
    tft.setCursor(10, 80);
    tft.println("Expected: 320x240");
  }
}

void drawFullScreenPattern(int width, int height) {
  // Draw gradient bars to test full screen coverage
  for (int y = 0; y < height; y += 20) {
    uint16_t color = tft.color565(
      (y * 255) / height,  // Red gradient
      255 - (y * 255) / height,  // Green gradient
      128  // Blue constant
    );
    tft.fillRect(0, y, width, 20, color);
  }

  // Draw vertical lines to test width
  for (int x = 0; x < width; x += 40) {
    tft.drawLine(x, 0, x, height, TFT_WHITE);
  }

  // Draw border
  tft.drawRect(0, 0, width, height, TFT_WHITE);
  tft.drawRect(2, 2, width-4, height-4, TFT_WHITE);

  // Corner markers
  tft.fillCircle(10, 10, 5, TFT_RED);
  tft.fillCircle(width-10, 10, 5, TFT_GREEN);
  tft.fillCircle(10, height-10, 5, TFT_BLUE);
  tft.fillCircle(width-10, height-10, 5, TFT_YELLOW);
}

void loop() {
  // Continuous test - blink corner markers
  static unsigned long lastBlink = 0;
  static bool blinkState = false;

  if (millis() - lastBlink > 1000) {
    blinkState = !blinkState;

    int width = tft.width();
    int height = tft.height();

    if (blinkState) {
      // Bright corners
      tft.fillCircle(10, 10, 5, TFT_WHITE);
      tft.fillCircle(width-10, 10, 5, TFT_WHITE);
      tft.fillCircle(10, height-10, 5, TFT_WHITE);
      tft.fillCircle(width-10, height-10, 5, TFT_WHITE);
    } else {
      // Original colors
      tft.fillCircle(10, 10, 5, TFT_RED);
      tft.fillCircle(width-10, 10, 5, TFT_GREEN);
      tft.fillCircle(10, height-10, 5, TFT_BLUE);
      tft.fillCircle(width-10, height-10, 5, TFT_YELLOW);
    }

    lastBlink = millis();
  }

  delay(10);
}
