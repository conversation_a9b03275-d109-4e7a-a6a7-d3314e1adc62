#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <TFT_eSPI.h>

// TFT Display
TFT_eSPI tft = TFT_eSPI();

// WiFi credentials - replace with your WiFi
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// DESS Monitor API URL - replace with your actual URL
const char* apiURL = "https://web.dessmonitor.com/public/?sign=8743221c28ad40664baa48193bbf4b03caa726f1&salt=1748162984217&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576&action=querySPDeviceLastData&source=1&devcode=2376&pn=Q0046526082082&devaddr=1&sn=Q0046526082082094801&i18n=en_US";

// Data refresh interval
const unsigned long REFRESH_INTERVAL = 30000; // 30 seconds
unsigned long lastUpdate = 0;

// Simple data structure
struct SimpleData {
  float pvPower = 0;
  float batteryVoltage = 0;
  float outputPower = 0;
  float loadPercent = 0;
  String timestamp = "";
  bool valid = false;
};

SimpleData data;

// Colors
#define TFT_GREY 0x5AEB

// Function declarations
void fetchData();
void updateDisplay();

void setup() {
  // Initialize TFT
  tft.init();
  tft.setRotation(1); // Landscape
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE, TFT_BLACK);

  // Show startup message
  tft.setTextSize(2);
  tft.setCursor(10, 50);
  tft.println("DESS Monitor");
  tft.setTextSize(1);
  tft.setCursor(10, 80);
  tft.println("ESP32-2432S028R");
  tft.setCursor(10, 100);
  tft.println("Starting...");

  delay(2000);

  // Connect to WiFi
  WiFi.begin(ssid, password);

  tft.setCursor(10, 120);
  tft.println("Connecting to WiFi...");

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    tft.print(".");
    attempts++;
  }

  if (WiFi.status() == WL_CONNECTED) {
    tft.fillScreen(TFT_BLACK);
    tft.setCursor(10, 50);
    tft.println("WiFi Connected!");
    tft.setCursor(10, 70);
    tft.print("IP: ");
    tft.println(WiFi.localIP());
    delay(2000);
  } else {
    tft.fillScreen(TFT_BLACK);
    tft.setCursor(10, 50);
    tft.setTextColor(TFT_RED, TFT_BLACK);
    tft.println("WiFi Failed!");
    tft.setCursor(10, 70);
    tft.println("Check credentials");
    while(1) delay(1000); // Stop here
  }

  // Initial data fetch
  fetchData();
}

void loop() {
  // Update data every 30 seconds
  if (millis() - lastUpdate >= REFRESH_INTERVAL) {
    fetchData();
    lastUpdate = millis();
  }

  // Update display
  updateDisplay();

  delay(2000); // Update display every 2 seconds
}

void fetchData() {
  if (WiFi.status() != WL_CONNECTED) {
    data.valid = false;
    return;
  }

  HTTPClient http;
  http.begin(apiURL);
  http.setTimeout(10000);

  int httpCode = http.GET();

  if (httpCode == 200) {
    String payload = http.getString();

    // Parse JSON
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, payload);

    if (!error && doc["err"] == 0) {
      data.timestamp = doc["dat"]["gts"].as<String>();

      // Parse PV Power
      if (doc["dat"]["pars"]["pv_"].is<JsonArray>()) {
        for (JsonObject param : doc["dat"]["pars"]["pv_"].as<JsonArray>()) {
          if (param["par"].as<String>() == "PV Power") {
            data.pvPower = param["val"].as<float>();
          }
        }
      }

      // Parse Battery Voltage
      if (doc["dat"]["pars"]["bt_"].is<JsonArray>()) {
        for (JsonObject param : doc["dat"]["pars"]["bt_"].as<JsonArray>()) {
          if (param["par"].as<String>() == "Battery Voltage") {
            data.batteryVoltage = param["val"].as<float>();
          }
        }
      }

      // Parse Output Power
      if (doc["dat"]["pars"]["ot_"].is<JsonArray>()) {
        for (JsonObject param : doc["dat"]["pars"]["ot_"].as<JsonArray>()) {
          if (param["par"].as<String>() == "Output Active Power") {
            data.outputPower = param["val"].as<float>();
          } else if (param["par"].as<String>() == "Load Percent") {
            data.loadPercent = param["val"].as<float>();
          }
        }
      }

      data.valid = true;
    } else {
      data.valid = false;
    }
  } else {
    data.valid = false;
  }

  http.end();
}

void updateDisplay() {
  tft.fillScreen(TFT_BLACK);

  // Header
  tft.setTextColor(TFT_CYAN, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("DESS Monitor");

  // Status
  if (data.valid) {
    tft.fillCircle(300, 20, 8, TFT_GREEN);
  } else {
    tft.fillCircle(300, 20, 8, TFT_RED);
  }

  // Timestamp
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(1);
  tft.setCursor(10, 35);
  tft.print("Last: ");
  if (data.timestamp.length() > 10) {
    tft.println(data.timestamp.substring(11, 19)); // Show time only
  }

  if (!data.valid) {
    tft.setTextColor(TFT_RED, TFT_BLACK);
    tft.setTextSize(2);
    tft.setCursor(50, 100);
    tft.println("No Data");
    return;
  }

  // Data display
  int y = 60;
  tft.setTextSize(1);

  // PV Power
  tft.setTextColor(TFT_YELLOW, TFT_BLACK);
  tft.setCursor(10, y);
  tft.println("SOLAR POWER");
  y += 15;
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setCursor(10, y);
  tft.printf("%.0f W", data.pvPower);
  y += 25;

  // Battery
  tft.setTextColor(TFT_GREEN, TFT_BLACK);
  tft.setCursor(10, y);
  tft.println("BATTERY");
  y += 15;
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setCursor(10, y);
  tft.printf("%.1f V", data.batteryVoltage);
  y += 25;

  // Output
  tft.setTextColor(TFT_MAGENTA, TFT_BLACK);
  tft.setCursor(10, y);
  tft.println("OUTPUT");
  y += 15;
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setCursor(10, y);
  tft.printf("%.0f W", data.outputPower);
  y += 15;
  tft.setCursor(10, y);
  tft.printf("Load: %.0f%%", data.loadPercent);

  // WiFi status
  tft.setCursor(200, 220);
  tft.printf("WiFi: %d dBm", WiFi.RSSI());
}
