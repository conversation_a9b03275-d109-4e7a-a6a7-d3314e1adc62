#include "display.h"
#include "config.h"

Display::Display() : tft(TFT_eSPI()) {
}

void Display::init() {
  tft.init();
  tft.setRotation(ROTATION);
  tft.fillScreen(COLOR_BACKGROUND);
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  
  // Initialize backlight
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  
  DEBUG_PRINTLN("Display initialized");
}

void Display::showStartupScreen() {
  tft.fillScreen(COLOR_BACKGROUND);
  
  // Title
  tft.setTextColor(COLOR_HEADER, COLOR_BACKGROUND);
  tft.setTextSize(3);
  tft.setCursor(20, 40);
  tft.println("DESS Monitor");
  
  // Subtitle
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setTextSize(2);
  tft.setCursor(30, 80);
  tft.println("ESP32-2432S028R");
  
  // Description
  tft.setTextSize(1);
  tft.setCursor(50, 120);
  tft.println("Inverter Data Display");
  
  // Status
  tft.setCursor(80, 140);
  tft.println("Starting...");
  
  delay(3000);
}

void Display::showWiFiSetup() {
  tft.fillScreen(COLOR_BACKGROUND);
  tft.setTextColor(COLOR_HEADER, COLOR_BACKGROUND);
  tft.setTextSize(2);
  tft.setCursor(10, 50);
  tft.println("WiFi Setup");
  
  tft.setTextSize(1);
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(10, 80);
  tft.println("1. Connect to 'ESP32-DESS' AP");
  tft.setCursor(10, 100);
  tft.println("2. Go to ***********");
  tft.setCursor(10, 120);
  tft.println("3. Configure WiFi settings");
  tft.setCursor(10, 140);
  tft.println("4. Save and restart");
}

void Display::showWiFiConnected(IPAddress ip) {
  tft.fillScreen(COLOR_BACKGROUND);
  tft.setTextColor(COLOR_SUCCESS, COLOR_BACKGROUND);
  tft.setTextSize(2);
  tft.setCursor(10, 50);
  tft.println("WiFi Connected!");
  
  tft.setTextSize(1);
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(10, 80);
  tft.print("IP: ");
  tft.println(ip);
  
  tft.setCursor(10, 100);
  tft.println("Starting data collection...");
}

void Display::showError(const String& message) {
  tft.fillScreen(COLOR_BACKGROUND);
  tft.setTextColor(COLOR_ERROR, COLOR_BACKGROUND);
  tft.setTextSize(2);
  tft.setCursor(10, 50);
  tft.println("ERROR");
  
  tft.setTextSize(1);
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(10, 80);
  tft.println(message);
}

void Display::updateData(const InverterData& data) {
  static unsigned long lastUpdate = 0;
  
  // Limit update frequency to prevent flicker
  if (millis() - lastUpdate < DISPLAY_UPDATE_INTERVAL) {
    return;
  }
  lastUpdate = millis();
  
  tft.fillScreen(COLOR_BACKGROUND);
  
  // Header
  drawHeader(data);
  
  if (!data.dataValid) {
    showNoData();
    return;
  }
  
  // Main data display
  drawSolarSection(data);
  drawBatterySection(data);
  drawGridSection(data);
  drawOutputSection(data);
  drawSystemSection(data);
  drawWiFiStatus();
}

void Display::drawHeader(const InverterData& data) {
  // Title
  tft.setTextColor(COLOR_HEADER, COLOR_BACKGROUND);
  tft.setTextSize(2);
  tft.setCursor(10, 5);
  tft.println("DESS Monitor");
  
  // Status indicator
  if (data.dataValid) {
    tft.fillCircle(300, 15, 8, COLOR_SUCCESS);
  } else {
    tft.fillCircle(300, 15, 8, COLOR_ERROR);
  }
  
  // Timestamp
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setTextSize(1);
  tft.setCursor(10, 25);
  tft.print("Last Update: ");
  if (data.timestamp.length() > 16) {
    tft.println(data.timestamp.substring(11, 19)); // Show only time
  } else {
    tft.println(data.timestamp);
  }
}

void Display::showNoData() {
  tft.setTextColor(COLOR_ERROR, COLOR_BACKGROUND);
  tft.setTextSize(2);
  tft.setCursor(50, 100);
  tft.println("No Data Available");
  
  tft.setTextSize(1);
  tft.setCursor(70, 130);
  tft.println("Check WiFi connection");
}

void Display::drawSolarSection(const InverterData& data) {
  int x = 10, y = 45;
  
  tft.setTextColor(COLOR_SOLAR, COLOR_BACKGROUND);
  tft.setTextSize(1);
  tft.setCursor(x, y);
  tft.println("SOLAR");
  y += 15;
  
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(x, y);
  tft.printf("%.1fV", data.pvVoltage);
  y += 12;
  
  tft.setCursor(x, y);
  tft.printf("%.1fA", data.pvCurrent);
  y += 12;
  
  tft.setCursor(x, y);
  tft.printf("%.0fW", data.pvPower);
}

void Display::drawBatterySection(const InverterData& data) {
  int x = 80, y = 45;
  
  tft.setTextColor(COLOR_BATTERY, COLOR_BACKGROUND);
  tft.setTextSize(1);
  tft.setCursor(x, y);
  tft.println("BATTERY");
  y += 15;
  
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(x, y);
  tft.printf("%.1fV", data.batteryVoltage);
  y += 12;
  
  tft.setCursor(x, y);
  if (data.batteryCurrent >= 0) {
    tft.setTextColor(COLOR_SUCCESS, COLOR_BACKGROUND);
    tft.printf("+%.1fA", data.batteryCurrent);
  } else {
    tft.setTextColor(COLOR_WARNING, COLOR_BACKGROUND);
    tft.printf("%.1fA", data.batteryCurrent);
  }
  
  // Battery level indicator (simplified)
  y += 15;
  int battLevel = map(data.batteryVoltage * 10, 480, 580, 0, 100); // 48V-58V range
  battLevel = constrain(battLevel, 0, 100);
  
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(x, y);
  tft.printf("%d%%", battLevel);
}

void Display::drawGridSection(const InverterData& data) {
  int x = 160, y = 45;
  
  tft.setTextColor(COLOR_GRID, COLOR_BACKGROUND);
  tft.setTextSize(1);
  tft.setCursor(x, y);
  tft.println("GRID");
  y += 15;
  
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(x, y);
  tft.printf("%.1fV", data.gridVoltage);
  y += 12;
  
  tft.setCursor(x, y);
  tft.printf("%.2fHz", data.gridFrequency);
}

void Display::drawOutputSection(const InverterData& data) {
  int x = 230, y = 45;
  
  tft.setTextColor(COLOR_OUTPUT, COLOR_BACKGROUND);
  tft.setTextSize(1);
  tft.setCursor(x, y);
  tft.println("OUTPUT");
  y += 15;
  
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(x, y);
  tft.printf("%.1fV", data.outputVoltage);
  y += 12;
  
  tft.setCursor(x, y);
  tft.printf("%.0fW", data.outputPower);
  y += 12;
  
  tft.setCursor(x, y);
  tft.printf("%.0f%%", data.loadPercent);
}

void Display::drawSystemSection(const InverterData& data) {
  int x = 10, y = 140;
  
  tft.setTextColor(COLOR_SYSTEM, COLOR_BACKGROUND);
  tft.setTextSize(1);
  tft.setCursor(x, y);
  tft.println("SYSTEM");
  y += 15;
  
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setCursor(x, y);
  if (data.operatingMode.length() > 15) {
    tft.println(data.operatingMode.substring(0, 15));
  } else {
    tft.println(data.operatingMode);
  }
  y += 12;
  
  tft.setCursor(x, y);
  tft.printf("DC:%d°C INV:%d°C", data.dcTemp, data.invTemp);
}

void Display::drawWiFiStatus() {
  int rssi = WiFi.RSSI();
  int x = 250, y = 220;
  
  tft.setTextColor(COLOR_TEXT, COLOR_BACKGROUND);
  tft.setTextSize(1);
  tft.setCursor(x, y);
  tft.printf("WiFi:%ddBm", rssi);
  
  // WiFi signal strength bars
  int bars = map(rssi, -100, -30, 0, 4);
  bars = constrain(bars, 0, 4);
  
  for (int i = 0; i < 4; i++) {
    int barHeight = (i + 1) * 3;
    uint16_t color = (i < bars) ? COLOR_SUCCESS : COLOR_TEXT;
    tft.fillRect(x + 60 + (i * 4), y + 10 - barHeight, 3, barHeight, color);
  }
}

void Display::setBrightness(uint8_t brightness) {
  // Simple on/off control for now
  digitalWrite(TFT_BL, brightness > 0 ? HIGH : LOW);
}
