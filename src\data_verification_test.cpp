#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <TFT_eSPI.h>

// TFT Display
TFT_eSPI tft = TFT_eSPI();

// Backlight pin
#define TFT_BL 21

// DESS Monitor API URL
const char* apiURL = "https://web.dessmonitor.com/public/?sign=8743221c28ad40664baa48193bbf4b03caa726f1&salt=1748162984217&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576&action=querySPDeviceLastData&source=1&devcode=2376&pn=Q0046526082082&devaddr=1&sn=Q0046526082082094801&i18n=en_US";

// WiFi credentials - แก้ไขตามของคุณ
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// Colors
#define COLOR_BG           0x0000
#define COLOR_TEXT_PRIMARY 0xFFFF
#define COLOR_TEXT_SECONDARY 0x8410
#define COLOR_SUCCESS      0x07E0
#define COLOR_WARNING      0xFFE0
#define COLOR_ERROR        0xF800
#define COLOR_HEADER       0x1082

// Data structure for complete verification
struct CompleteInverterData {
  // Grid data
  float gridVoltage = 0;
  float gridFrequency = 0;
  float gridPower = 0;
  
  // PV data
  float pvVoltage = 0;
  float pvCurrent = 0;
  float pvPower = 0;
  float pvChargingCurrent = 0;
  
  // Battery data
  float batteryVoltage = 0;
  float batteryCurrent = 0;
  
  // Output data
  float outputVoltage = 0;
  float outputCurrent = 0;
  float outputActivePower = 0;
  float outputApparentPower = 0;
  float outputFrequency = 0;
  float loadPercent = 0;
  
  // System data
  int dcTemp = 0;
  int invTemp = 0;
  String operatingMode = "";
  String outputPriority = "";
  String chargerSourcePriority = "";
  float acChargingCurrent = 0;
  
  // Metadata
  String timestamp = "";
  bool dataValid = false;
  int totalParameters = 0;
  int parsedParameters = 0;
};

CompleteInverterData data;
int currentPage = 0;
const int TOTAL_PAGES = 4;

// Function declarations
void setupWiFi();
void fetchAndAnalyzeData();
void displayDataPage(int page);
void displayRawDataAnalysis();
void displayMissingDataAnalysis();
void displayComparisonSummary();
void displayParameterList();

void setup() {
  delay(1000);
  
  // Initialize backlight
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  
  // Initialize TFT
  tft.init();
  tft.setRotation(1);
  tft.fillScreen(COLOR_BG);
  
  // Show startup
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setTextSize(2);
  tft.setCursor(50, 80);
  tft.println("Data Verification");
  tft.setTextSize(1);
  tft.setCursor(80, 120);
  tft.println("Starting analysis...");
  
  delay(2000);
  
  // Setup WiFi
  setupWiFi();
  
  // Fetch and analyze data
  fetchAndAnalyzeData();
  
  // Show first page
  displayDataPage(0);
}

void loop() {
  static unsigned long lastPageChange = 0;
  
  // Auto-cycle through pages every 5 seconds
  if (millis() - lastPageChange > 5000) {
    currentPage = (currentPage + 1) % TOTAL_PAGES;
    displayDataPage(currentPage);
    lastPageChange = millis();
  }
  
  delay(100);
}

void setupWiFi() {
  tft.fillScreen(COLOR_BG);
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setTextSize(1);
  tft.setCursor(10, 50);
  tft.println("Connecting to WiFi...");
  
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    tft.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
    tft.setCursor(10, 80);
    tft.println("WiFi Connected!");
    tft.setCursor(10, 100);
    tft.print("IP: ");
    tft.println(WiFi.localIP());
  } else {
    tft.setTextColor(COLOR_ERROR, COLOR_BG);
    tft.setCursor(10, 80);
    tft.println("WiFi Failed!");
    while(1) delay(1000);
  }
  
  delay(2000);
}

void fetchAndAnalyzeData() {
  tft.fillScreen(COLOR_BG);
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(10, 50);
  tft.println("Fetching API data...");
  
  HTTPClient http;
  http.begin(apiURL);
  http.setTimeout(15000);
  
  int httpCode = http.GET();
  
  if (httpCode == 200) {
    String payload = http.getString();
    
    tft.setCursor(10, 70);
    tft.printf("Response size: %d bytes", payload.length());
    
    // Parse JSON
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, payload);
    
    if (!error && doc["err"] == 0) {
      data.timestamp = doc["dat"]["gts"].as<String>();
      data.dataValid = true;
      
      tft.setCursor(10, 90);
      tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
      tft.println("JSON parsed successfully!");
      
      // Count total parameters
      JsonObject pars = doc["dat"]["pars"];
      data.totalParameters = 0;
      data.parsedParameters = 0;
      
      // Count all parameters
      for (JsonPair group : pars) {
        if (group.value().is<JsonArray>()) {
          data.totalParameters += group.value().as<JsonArray>().size();
        }
      }
      
      // Parse Grid data (gd_)
      if (pars["gd_"].is<JsonArray>()) {
        for (JsonObject param : pars["gd_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          float value = param["val"].as<float>();
          
          if (parName == "Grid Voltage") {
            data.gridVoltage = value;
            data.parsedParameters++;
          } else if (parName == "Grid Frequency") {
            data.gridFrequency = value;
            data.parsedParameters++;
          } else if (parName == "Grid Power") {
            data.gridPower = value;
            data.parsedParameters++;
          }
        }
      }
      
      // Parse PV data (pv_)
      if (pars["pv_"].is<JsonArray>()) {
        for (JsonObject param : pars["pv_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          float value = param["val"].as<float>();
          
          if (parName == "PV Voltage") {
            data.pvVoltage = value;
            data.parsedParameters++;
          } else if (parName == "PV Current") {
            data.pvCurrent = value;
            data.parsedParameters++;
          } else if (parName == "PV Power") {
            data.pvPower = value;
            data.parsedParameters++;
          } else if (parName == "PV Charging Current") {
            data.pvChargingCurrent = value;
            data.parsedParameters++;
          }
        }
      }
      
      // Parse Battery data (bt_)
      if (pars["bt_"].is<JsonArray>()) {
        for (JsonObject param : pars["bt_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          float value = param["val"].as<float>();
          
          if (parName == "Battery Voltage") {
            data.batteryVoltage = value;
            data.parsedParameters++;
          } else if (parName == "Battery Current") {
            data.batteryCurrent = value;
            data.parsedParameters++;
          }
        }
      }
      
      // Parse Output data (ot_)
      if (pars["ot_"].is<JsonArray>()) {
        for (JsonObject param : pars["ot_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          float value = param["val"].as<float>();
          
          if (parName == "Output Voltage") {
            data.outputVoltage = value;
            data.parsedParameters++;
          } else if (parName == "Output Current") {
            data.outputCurrent = value;
            data.parsedParameters++;
          } else if (parName == "Output Active Power") {
            data.outputActivePower = value;
            data.parsedParameters++;
          } else if (parName == "Output Apparent Power") {
            data.outputApparentPower = value;
            data.parsedParameters++;
          } else if (parName == "Output Frequency") {
            data.outputFrequency = value;
            data.parsedParameters++;
          } else if (parName == "Load Percent") {
            data.loadPercent = value;
            data.parsedParameters++;
          }
        }
      }
      
      // Parse System data (sy_)
      if (pars["sy_"].is<JsonArray>()) {
        for (JsonObject param : pars["sy_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          
          if (parName == "DC Module Termperature") {
            data.dcTemp = param["val"].as<int>();
            data.parsedParameters++;
          } else if (parName == "INV Module Termperature") {
            data.invTemp = param["val"].as<int>();
            data.parsedParameters++;
          } else if (parName == "Operating Mode") {
            data.operatingMode = param["val"].as<String>();
            data.parsedParameters++;
          } else if (parName == "Output Priority") {
            data.outputPriority = param["val"].as<String>();
            data.parsedParameters++;
          } else if (parName == "Charger Source Priority") {
            data.chargerSourcePriority = param["val"].as<String>();
            data.parsedParameters++;
          } else if (parName == "AC Charging Current") {
            data.acChargingCurrent = param["val"].as<float>();
            data.parsedParameters++;
          }
        }
      }
      
      tft.setCursor(10, 110);
      tft.printf("Parsed: %d/%d parameters", data.parsedParameters, data.totalParameters);
      
    } else {
      tft.setTextColor(COLOR_ERROR, COLOR_BG);
      tft.setCursor(10, 90);
      tft.println("JSON parsing failed!");
      data.dataValid = false;
    }
  } else {
    tft.setTextColor(COLOR_ERROR, COLOR_BG);
    tft.setCursor(10, 70);
    tft.printf("HTTP error: %d", httpCode);
    data.dataValid = false;
  }
  
  http.end();
  delay(3000);
}

void displayDataPage(int page) {
  tft.fillScreen(COLOR_BG);
  
  // Header
  tft.fillRect(0, 0, 320, 25, COLOR_HEADER);
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_HEADER);
  tft.setTextSize(1);
  tft.setCursor(5, 8);
  tft.printf("Data Verification - Page %d/%d", page + 1, TOTAL_PAGES);
  
  switch (page) {
    case 0:
      displayRawDataAnalysis();
      break;
    case 1:
      displayParameterList();
      break;
    case 2:
      displayMissingDataAnalysis();
      break;
    case 3:
      displayComparisonSummary();
      break;
  }
}

void displayRawDataAnalysis() {
  int y = 35;
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setTextSize(1);
  
  tft.setCursor(5, y);
  tft.println("RAW DATA ANALYSIS:");
  y += 15;
  
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setCursor(5, y);
  tft.printf("Timestamp: %s", data.timestamp.c_str());
  y += 12;
  
  tft.setCursor(5, y);
  tft.printf("Total Parameters: %d", data.totalParameters);
  y += 12;
  
  tft.setCursor(5, y);
  tft.printf("Parsed Parameters: %d", data.parsedParameters);
  y += 12;
  
  tft.setCursor(5, y);
  float parseRate = (float)data.parsedParameters / data.totalParameters * 100;
  tft.printf("Parse Rate: %.1f%%", parseRate);
  y += 20;
  
  // Current values
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(5, y);
  tft.println("CURRENT VALUES:");
  y += 15;
  
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setCursor(5, y);
  tft.printf("PV: %.1fV %.1fA %.0fW", data.pvVoltage, data.pvCurrent, data.pvPower);
  y += 12;
  
  tft.setCursor(5, y);
  tft.printf("Battery: %.1fV %.1fA", data.batteryVoltage, data.batteryCurrent);
  y += 12;
  
  tft.setCursor(5, y);
  tft.printf("Grid: %.1fV %.2fHz", data.gridVoltage, data.gridFrequency);
  y += 12;
  
  tft.setCursor(5, y);
  tft.printf("Output: %.1fV %.0fW %.0f%%", data.outputVoltage, data.outputActivePower, data.loadPercent);
  y += 12;
  
  tft.setCursor(5, y);
  tft.printf("Temp: DC=%d°C INV=%d°C", data.dcTemp, data.invTemp);
  y += 12;
  
  tft.setCursor(5, y);
  tft.printf("Mode: %s", data.operatingMode.c_str());
}

void displayParameterList() {
  int y = 35;
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setTextSize(1);
  
  tft.setCursor(5, y);
  tft.println("PARAMETER COVERAGE:");
  y += 15;
  
  // Grid parameters
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setCursor(5, y);
  tft.println("GRID:");
  y += 12;
  
  tft.setCursor(15, y);
  tft.printf("Voltage: %s", data.gridVoltage > 0 ? "OK" : "MISSING");
  y += 10;
  
  tft.setCursor(15, y);
  tft.printf("Frequency: %s", data.gridFrequency > 0 ? "OK" : "MISSING");
  y += 10;
  
  tft.setCursor(15, y);
  tft.printf("Power: %s", data.gridPower != 0 ? "OK" : "MISSING");
  y += 15;
  
  // PV parameters
  tft.setCursor(5, y);
  tft.println("PV:");
  y += 12;
  
  tft.setCursor(15, y);
  tft.printf("Voltage: %s", data.pvVoltage > 0 ? "OK" : "MISSING");
  y += 10;
  
  tft.setCursor(15, y);
  tft.printf("Current: %s", data.pvCurrent >= 0 ? "OK" : "MISSING");
  y += 10;
  
  tft.setCursor(15, y);
  tft.printf("Power: %s", data.pvPower >= 0 ? "OK" : "MISSING");
  y += 15;
  
  // Battery parameters
  tft.setCursor(5, y);
  tft.println("BATTERY:");
  y += 12;
  
  tft.setCursor(15, y);
  tft.printf("Voltage: %s", data.batteryVoltage > 0 ? "OK" : "MISSING");
  y += 10;
  
  tft.setCursor(15, y);
  tft.printf("Current: %s", data.batteryCurrent != 0 ? "OK" : "MISSING");
}

void displayMissingDataAnalysis() {
  int y = 35;
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setTextSize(1);
  
  tft.setCursor(5, y);
  tft.println("MISSING DATA ANALYSIS:");
  y += 15;
  
  int missingCount = 0;
  
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  
  // Check for missing critical data
  if (data.gridPower == 0) {
    tft.setCursor(5, y);
    tft.setTextColor(COLOR_WARNING, COLOR_BG);
    tft.println("- Grid Power not available");
    y += 12;
    missingCount++;
  }
  
  if (data.pvChargingCurrent == 0) {
    tft.setCursor(5, y);
    tft.setTextColor(COLOR_WARNING, COLOR_BG);
    tft.println("- PV Charging Current missing");
    y += 12;
    missingCount++;
  }
  
  if (data.outputApparentPower == 0) {
    tft.setCursor(5, y);
    tft.setTextColor(COLOR_WARNING, COLOR_BG);
    tft.println("- Output Apparent Power missing");
    y += 12;
    missingCount++;
  }
  
  if (data.outputFrequency == 0) {
    tft.setCursor(5, y);
    tft.setTextColor(COLOR_WARNING, COLOR_BG);
    tft.println("- Output Frequency missing");
    y += 12;
    missingCount++;
  }
  
  if (data.outputPriority.length() == 0) {
    tft.setCursor(5, y);
    tft.setTextColor(COLOR_WARNING, COLOR_BG);
    tft.println("- Output Priority missing");
    y += 12;
    missingCount++;
  }
  
  if (data.chargerSourcePriority.length() == 0) {
    tft.setCursor(5, y);
    tft.setTextColor(COLOR_WARNING, COLOR_BG);
    tft.println("- Charger Source Priority missing");
    y += 12;
    missingCount++;
  }
  
  if (data.acChargingCurrent == 0) {
    tft.setCursor(5, y);
    tft.setTextColor(COLOR_WARNING, COLOR_BG);
    tft.println("- AC Charging Current missing");
    y += 12;
    missingCount++;
  }
  
  if (missingCount == 0) {
    tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
    tft.setCursor(5, y);
    tft.println("All critical data available!");
  } else {
    tft.setTextColor(COLOR_ERROR, COLOR_BG);
    tft.setCursor(5, y + 15);
    tft.printf("Total missing: %d parameters", missingCount);
  }
}

void displayComparisonSummary() {
  int y = 35;
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setTextSize(1);
  
  tft.setCursor(5, y);
  tft.println("DISPLAY vs API COMPARISON:");
  y += 15;
  
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  
  // What's displayed in Pro version
  tft.setCursor(5, y);
  tft.println("DISPLAYED IN PRO UI:");
  y += 12;
  
  tft.setCursor(10, y);
  tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
  tft.println("✓ Solar: Power, Voltage, Current");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("✓ Battery: Voltage, Current, Level%");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("✓ Grid: Voltage, Frequency, Status");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("✓ Output: Power, Voltage, Current, Load%");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("✓ System: DC Temp, INV Temp, Mode");
  y += 15;
  
  // What's available but not displayed
  tft.setTextColor(COLOR_WARNING, COLOR_BG);
  tft.setCursor(5, y);
  tft.println("AVAILABLE BUT NOT DISPLAYED:");
  y += 12;
  
  tft.setCursor(10, y);
  tft.println("• Grid Power");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("• PV Charging Current");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("• Output Apparent Power");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("• Output Frequency");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("• Priority Settings");
  y += 10;
  
  tft.setCursor(10, y);
  tft.println("• AC Charging Current");
  
  // Coverage percentage
  y += 20;
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(5, y);
  int displayedParams = 11; // Count of parameters shown in Pro UI
  float coverage = (float)displayedParams / data.parsedParameters * 100;
  tft.printf("UI Coverage: %.1f%% (%d/%d)", coverage, displayedParams, data.parsedParameters);
}
