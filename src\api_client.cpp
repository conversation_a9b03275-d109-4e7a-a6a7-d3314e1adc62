#include "api_client.h"
#include "config.h"

APIClient::APIClient() : http() {
}

bool APIClient::begin(const String& url) {
  apiURL = url;
  return true;
}

bool APIClient::fetchData(InverterData& data) {
  if (WiFi.status() != WL_CONNECTED) {
    DEBUG_PRINTLN("WiFi not connected");
    data.dataValid = false;
    data.errorCount++;
    return false;
  }

  http.begin(apiURL);
  http.setTimeout(API_TIMEOUT);
  http.addHeader("User-Agent", "ESP32-DESS-Monitor/1.0");

  DEBUG_PRINTLN("Fetching inverter data...");

  int httpResponseCode = http.GET();

  if (httpResponseCode == 200) {
    String payload = http.getString();
    DEBUG_PRINTLN("Data received, parsing...");

    bool parseSuccess = parseJSON(payload, data);

    if (parseSuccess) {
      data.dataValid = true;
      data.lastUpdate = millis();
      data.errorCount = 0;
      DEBUG_PRINTLN("Data parsed successfully");
    } else {
      data.dataValid = false;
      data.errorCount++;
      DEBUG_PRINTLN("JSON parsing failed");
    }

    http.end();
    return parseSuccess;

  } else {
    DEBUG_PRINTF("HTTP error: %d\n", httpResponseCode);
    data.dataValid = false;
    data.errorCount++;
    http.end();
    return false;
  }
}

bool APIClient::parseJSON(const String& json, InverterData& data) {
  JsonDocument doc; // Use JsonDocument instead of deprecated DynamicJsonDocument
  DeserializationError error = deserializeJson(doc, json);

  if (error) {
    DEBUG_PRINTF("JSON deserialization failed: %s\n", error.c_str());
    return false;
  }

  // Check API response status
  if (doc["err"].as<int>() != 0) {
    DEBUG_PRINTF("API error: %s\n", doc["desc"].as<String>().c_str());
    return false;
  }

  // Extract timestamp
  data.timestamp = doc["dat"]["gts"].as<String>();

  // Parse parameters
  JsonObject pars = doc["dat"]["pars"];

  // Parse each parameter group
  parseGridData(pars, data);
  parsePVData(pars, data);
  parseBatteryData(pars, data);
  parseOutputData(pars, data);
  parseSystemData(pars, data);

  return true;
}

void APIClient::parseGridData(JsonObject& pars, InverterData& data) {
  if (pars["gd_"].is<JsonArray>()) {
    for (JsonObject param : pars["gd_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();

      if (parName == "Grid Voltage") {
        data.gridVoltage = value;
      } else if (parName == "Grid Frequency") {
        data.gridFrequency = value;
      } else if (parName == "Grid Power") {
        data.gridPower = value;
      }
    }
  }
}

void APIClient::parsePVData(JsonObject& pars, InverterData& data) {
  if (pars["pv_"].is<JsonArray>()) {
    for (JsonObject param : pars["pv_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();

      if (parName == "PV Voltage") {
        data.pvVoltage = value;
      } else if (parName == "PV Current") {
        data.pvCurrent = value;
      } else if (parName == "PV Power") {
        data.pvPower = value;
      } else if (parName == "PV Charging Current") {
        data.pvChargingCurrent = value;
      }
    }
  }
}

void APIClient::parseBatteryData(JsonObject& pars, InverterData& data) {
  if (pars["bt_"].is<JsonArray>()) {
    for (JsonObject param : pars["bt_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();

      if (parName == "Battery Voltage") {
        data.batteryVoltage = value;
      } else if (parName == "Battery Current") {
        data.batteryCurrent = value;
      }
    }
  }
}

void APIClient::parseOutputData(JsonObject& pars, InverterData& data) {
  if (pars["ot_"].is<JsonArray>()) {
    for (JsonObject param : pars["ot_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();

      if (parName == "Output Voltage") {
        data.outputVoltage = value;
      } else if (parName == "Output Current") {
        data.outputCurrent = value;
      } else if (parName == "Output Active Power") {
        data.outputPower = value;
      } else if (parName == "Output Apparent Power") {
        data.outputApparentPower = value;
      } else if (parName == "Load Percent") {
        data.loadPercent = value;
      } else if (parName == "Output Frequency") {
        data.outputFrequency = value;
      }
    }
  }
}

void APIClient::parseSystemData(JsonObject& pars, InverterData& data) {
  if (pars["sy_"].is<JsonArray>()) {
    for (JsonObject param : pars["sy_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();

      if (parName == "DC Module Termperature") {
        data.dcTemp = param["val"].as<int>();
      } else if (parName == "INV Module Termperature") {
        data.invTemp = param["val"].as<int>();
      } else if (parName == "Operating Mode") {
        data.operatingMode = param["val"].as<String>();
      } else if (parName == "Output Priority") {
        data.outputPriority = param["val"].as<String>();
      } else if (parName == "Charger Source Priority") {
        data.chargerSourcePriority = param["val"].as<String>();
      } else if (parName == "AC Charging Current") {
        data.acChargingCurrent = param["val"].as<float>();
      }
    }
  }
}

String APIClient::getLastError() const {
  return lastError;
}

void APIClient::setURL(const String& url) {
  apiURL = url;
}
