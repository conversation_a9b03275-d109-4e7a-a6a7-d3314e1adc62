#include <Arduino.h>
#include <TFT_eSPI.h>

// Create TFT instance
TFT_eSPI tft = TFT_eSPI();

// Backlight pin
#define TFT_BL 21

void setup() {
  delay(1000);  // Wait for power stabilization
  
  // STEP 1: Turn on backlight FIRST!
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);  // Turn ON backlight
  delay(100);
  
  // STEP 2: Initialize TFT
  tft.init();
  delay(100);
  
  // STEP 3: Test different rotations
  for (int rotation = 0; rotation < 4; rotation++) {
    tft.setRotation(rotation);
    
    // Test basic colors
    tft.fillScreen(TFT_BLACK);
    delay(300);
    
    tft.fillScreen(TFT_RED);
    delay(500);
    
    tft.fillScreen(TFT_GREEN);
    delay(500);
    
    tft.fillScreen(TFT_BLUE);
    delay(500);
    
    tft.fillScreen(TFT_WHITE);
    delay(500);
    
    // Test text
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_CYAN, TFT_BLACK);
    tft.setTextSize(2);
    tft.setCursor(10, 10);
    tft.printf("Rotation: %d", rotation);
    
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.setCursor(10, 40);
    tft.println("ESP32-2432S028R");
    
    tft.setCursor(10, 70);
    tft.println("Display Test");
    
    tft.setTextColor(TFT_YELLOW, TFT_BLACK);
    tft.setCursor(10, 100);
    tft.println("If you see this,");
    tft.setCursor(10, 120);
    tft.println("display is WORKING!");
    
    delay(3000);  // Show for 3 seconds
  }
  
  // Final test - set best rotation (usually 1 for landscape)
  tft.setRotation(1);
  tft.fillScreen(TFT_BLACK);
  
  tft.setTextColor(TFT_GREEN, TFT_BLACK);
  tft.setTextSize(3);
  tft.setCursor(20, 50);
  tft.println("SUCCESS!");
  
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(30, 100);
  tft.println("Display Working");
  
  tft.setTextSize(1);
  tft.setCursor(50, 140);
  tft.println("Ready for main program");
}

void loop() {
  // Continuous blink test
  static unsigned long lastBlink = 0;
  static bool blinkState = false;
  
  if (millis() - lastBlink > 1000) {
    blinkState = !blinkState;
    
    if (blinkState) {
      // Cyan screen
      tft.fillScreen(TFT_CYAN);
      tft.setTextColor(TFT_BLACK, TFT_CYAN);
      tft.setTextSize(3);
      tft.setCursor(50, 60);
      tft.println("WORKING!");
      
      tft.setTextSize(2);
      tft.setCursor(80, 100);
      tft.println("Display OK");
      
      tft.setTextSize(1);
      tft.setCursor(100, 130);
      tft.println("Backlight: ON");
      
      tft.setCursor(100, 150);
      tft.println("SPI: Connected");
      
    } else {
      // Magenta screen
      tft.fillScreen(TFT_MAGENTA);
      tft.setTextColor(TFT_WHITE, TFT_MAGENTA);
      tft.setTextSize(3);
      tft.setCursor(70, 60);
      tft.println("ESP32");
      
      tft.setTextSize(2);
      tft.setCursor(50, 100);
      tft.println("2432S028R");
      
      tft.setTextSize(1);
      tft.setCursor(80, 130);
      tft.println("TFT Display Test");
      
      tft.setCursor(90, 150);
      tft.println("All Systems GO!");
    }
    
    lastBlink = millis();
  }
  
  delay(10);  // Small delay to prevent watchdog issues
}
