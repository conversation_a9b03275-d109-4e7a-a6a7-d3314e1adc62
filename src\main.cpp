#include <Arduino.h>
#include <WiFi.h>
#include <WiFiManager.h>
#include "config.h"
#include "data_types.h"
#include "display.h"
#include "api_client.h"

// Global objects
Display display;
APIClient apiClient;
InverterData data;
SystemStatus systemStatus;

// Timing variables
unsigned long lastDataUpdate = 0;
unsigned long lastDisplayUpdate = 0;

// Function declarations
void setupWiFi();
void fetchData();

void setup() {
  // Serial.begin(115200);  // Commented out to avoid Serial issues
  // DEBUG_PRINTLN("ESP32-2432S028R DESS Monitor Starting...");

  // Initialize display
  display.init();
  display.showStartupScreen();

  // Initialize WiFi
  setupWiFi();

  // Initialize API client
  apiClient.begin(DEFAULT_API_URL);

  // Initial data fetch
  fetchData();

  // DEBUG_PRINTLN("Setup complete!");
}

void loop() {
  // Update system status
  systemStatus.updateUptime();
  systemStatus.wifiRSSI = WiFi.RSSI();

  // Check WiFi connection
  if (WiFi.status() != WL_CONNECTED) {
    DEBUG_PRINTLN("WiFi disconnected, attempting reconnection...");
    systemStatus.wifiStatus = ConnectionStatus::DISCONNECTED;
    display.showError("WiFi Disconnected");
    setupWiFi();
    return;
  } else {
    systemStatus.wifiStatus = ConnectionStatus::CONNECTED;
  }

  // Update data periodically
  if (millis() - lastDataUpdate >= DATA_REFRESH_INTERVAL) {
    fetchData();
    lastDataUpdate = millis();
  }

  // Update display
  if (millis() - lastDisplayUpdate >= DISPLAY_UPDATE_INTERVAL) {
    display.updateData(data);
    lastDisplayUpdate = millis();
  }

  delay(100); // Small delay to prevent watchdog issues
}

void setupWiFi() {
  WiFiManager wm;

  // Show WiFi setup screen
  display.showWiFiSetup();

  // Set custom AP name and timeout
  wm.setAPStaticIPConfig(IPAddress(192,168,4,1), IPAddress(192,168,4,1), IPAddress(255,255,255,0));
  wm.setConfigPortalTimeout(180); // 3 minutes timeout

  // Add custom parameters if needed
  // WiFiManagerParameter custom_api_url("api_url", "API URL", DEFAULT_API_URL, 200);
  // wm.addParameter(&custom_api_url);

  systemStatus.wifiStatus = ConnectionStatus::CONNECTING;

  // Try to connect, if it fails start AP mode
  if (!wm.autoConnect("ESP32-DESS")) {
    DEBUG_PRINTLN("Failed to connect and hit timeout");
    display.showError("WiFi Setup Failed");
    delay(3000);
    ESP.restart();
  }

  DEBUG_PRINTLN("WiFi connected!");
  DEBUG_PRINT("IP address: ");
  DEBUG_PRINTLN(WiFi.localIP());

  systemStatus.wifiStatus = ConnectionStatus::CONNECTED;
  systemStatus.ipAddress = WiFi.localIP();

  // Show connection success
  display.showWiFiConnected(WiFi.localIP());
  delay(2000);
}

void fetchData() {
  DEBUG_PRINTLN("Fetching inverter data...");
  systemStatus.dataStatus = DataStatus::FETCHING;

  bool success = apiClient.fetchData(data);

  if (success) {
    systemStatus.dataStatus = DataStatus::VALID;
    systemStatus.lastError = "";
    DEBUG_PRINTLN("Data fetch successful");
  } else {
    systemStatus.dataStatus = DataStatus::ERROR;
    systemStatus.lastError = apiClient.getLastError();
    DEBUG_PRINTF("Data fetch failed: %s\n", systemStatus.lastError.c_str());
  }
}
